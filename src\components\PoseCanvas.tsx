
import { forwardRef, useEffect } from 'react';
import { Pose } from '../types/exercise';
import { drawPose } from '../utils/poseDrawing';

interface PoseCanvasProps {
  poses: Pose[];
  isCorrect: boolean;
  width: number;
  height: number;
  className?: string;
}

export const PoseCanvas = forwardRef<HTMLCanvasElement, PoseCanvasProps>(
  ({ poses, isCorrect, width, height, className = '' }, ref) => {
    useEffect(() => {
      if (!ref || typeof ref === 'function') return;
      if (!ref.current) return;

      const canvas = ref.current;
      const ctx = canvas.getContext('2d');
      if (!ctx) return;

      // Clear canvas
      ctx.clearRect(0, 0, width, height);

      // Draw poses
      poses.forEach(pose => {
        drawPose(ctx, pose, isCorrect, width, height);
      });
    }, [poses, isCorrect, width, height, ref]);

    return (
      <canvas
        ref={ref}
        width={width}
        height={height}
        className={`absolute top-0 left-0 w-full h-full pointer-events-none ${className}`}
        style={{ transform: 'scaleX(-1)' }}
      />
    );
  }
);

PoseCanvas.displayName = 'PoseCanvas';
