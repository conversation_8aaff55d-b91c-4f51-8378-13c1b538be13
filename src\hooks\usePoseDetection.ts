
import { useState, useCallback, useRef } from 'react';
import { PoseLandmarker, FilesetResolver, DrawingUtils } from '@mediapipe/tasks-vision';
import { Exercise, Pose, ExerciseFeedback, ExerciseState } from '../types/exercise';
import { evaluateSquats } from '../utils/squatEvaluator';
import { evaluatePushUps } from '../utils/pushupEvaluator';

export const usePoseDetection = (
  videoRef: React.RefObject<HTMLVideoElement>,
  canvasRef: React.RefObject<HTMLCanvasElement>,
  currentExercise: Exercise | null
) => {
  const [poses, setPoses] = useState<Pose[]>([]);
  const [feedback, setFeedback] = useState<ExerciseFeedback | null>(null);
  const [isCorrect, setIsCorrect] = useState(false);
  const [repCount, setRepCount] = useState(0);
  
  const poseLandmarker = useRef<PoseLandmarker | null>(null);
  const exerciseState = useRef<ExerciseState>({
    phase: 'rest',
    repCount: 0,
    isCorrect: false,
    lastCorrectTime: 0
  });
  const animationId = useRef<number | null>(null);

  const initializePoseDetection = useCallback(async () => {
    // Don't initialize if already initialized
    if (poseLandmarker.current) {
      console.log("Pose detection already initialized");
      return;
    }

    try {
      console.log("Initializing pose detection...");
      const vision = await FilesetResolver.forVisionTasks(
        "https://cdn.jsdelivr.net/npm/@mediapipe/tasks-vision@0.10.0/wasm"
      );

      poseLandmarker.current = await PoseLandmarker.createFromOptions(vision, {
        baseOptions: {
          modelAssetPath: "https://storage.googleapis.com/mediapipe-models/pose_landmarker/pose_landmarker_lite/float16/1/pose_landmarker_lite.task",
          delegate: "GPU"
        },
        runningMode: "VIDEO",
        numPoses: 1
      });

      console.log("Pose detection initialized successfully");
    } catch (error) {
      console.error("Failed to initialize pose detection:", error);
      // Don't throw error, just log it so camera can still work
    }
  }, []);

  const evaluateExercise = useCallback((detectedPoses: Pose[]) => {
    if (!detectedPoses.length || !currentExercise) return;

    const pose = detectedPoses[0];
    let evaluation;

    switch (currentExercise) {
      case 'squats':
        evaluation = evaluateSquats(pose, exerciseState.current);
        break;
      case 'pushups':
        evaluation = evaluatePushUps(pose, exerciseState.current);
        break;
      default:
        return;
    }

    setFeedback(evaluation.feedback);
    setIsCorrect(evaluation.isCorrect);
    setRepCount(evaluation.repCount);
    
    exerciseState.current = evaluation.state;
  }, [currentExercise]);

  const detectPose = useCallback(async () => {
    // Check if we have all required elements and pose detection is ready
    if (!poseLandmarker.current || !videoRef.current || !canvasRef.current) {
      if (animationId.current) {
        requestAnimationFrame(detectPose);
      }
      return;
    }

    const video = videoRef.current;
    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');

    // Make sure video is ready and has valid dimensions
    if (video.readyState >= 2 && video.videoWidth > 0 && video.videoHeight > 0) {
      try {
        const results = poseLandmarker.current.detectForVideo(video, performance.now());

        // Clear canvas
        ctx?.clearRect(0, 0, canvas.width, canvas.height);

        if (results.landmarks && results.landmarks.length > 0) {
          const detectedPoses: Pose[] = results.landmarks.map((landmarks, index) => ({
            landmarks,
            worldLandmarks: results.worldLandmarks?.[index] || landmarks
          }));

          setPoses(detectedPoses);
          evaluateExercise(detectedPoses);
        } else {
          // Clear poses if no detection
          setPoses([]);
        }
      } catch (error) {
        console.error("Pose detection error:", error);
        // Don't stop the animation loop on error
      }
    }

    if (animationId.current) {
      requestAnimationFrame(detectPose);
    }
  }, [videoRef, canvasRef, evaluateExercise]);

  const startExercise = useCallback(async (exercise: Exercise) => {
    exerciseState.current = {
      phase: 'rest',
      repCount: 0,
      isCorrect: false,
      lastCorrectTime: 0
    };
    setRepCount(0);
    setFeedback({ message: `Starting ${exercise}...`, type: 'success' });
    
    animationId.current = requestAnimationFrame(detectPose);
  }, [detectPose]);

  const stopExercise = useCallback(() => {
    if (animationId.current) {
      cancelAnimationFrame(animationId.current);
      animationId.current = null;
    }
    setPoses([]);
    setFeedback(null);
    setIsCorrect(false);
    setRepCount(0);
  }, []);

  return {
    poses,
    feedback,
    isCorrect,
    repCount,
    initializePoseDetection,
    startExercise,
    stopExercise
  };
};
