
import { useState, useCallback, useRef } from 'react';
import { PoseLandmarker, FilesetResolver, DrawingUtils } from '@mediapipe/tasks-vision';
import { Exercise, Pose, ExerciseFeedback, ExerciseState } from '../types/exercise';
import { evaluateSquats } from '../utils/squatEvaluator';
import { evaluatePushUps } from '../utils/pushupEvaluator';

export const usePoseDetection = (
  videoRef: React.RefObject<HTMLVideoElement>,
  canvasRef: React.RefObject<HTMLCanvasElement>,
  currentExercise: Exercise | null
) => {
  const [poses, setPoses] = useState<Pose[]>([]);
  const [feedback, setFeedback] = useState<ExerciseFeedback | null>(null);
  const [isCorrect, setIsCorrect] = useState(false);
  const [repCount, setRepCount] = useState(0);
  
  const poseLandmarker = useRef<PoseLandmarker | null>(null);
  const exerciseState = useRef<ExerciseState>({
    phase: 'rest',
    repCount: 0,
    isCorrect: false,
    lastCorrectTime: 0
  });
  const animationId = useRef<number | null>(null);

  const initializePoseDetection = useCallback(async () => {
    // Don't initialize if already initialized
    if (poseLandmarker.current) {
      console.log("✅ Pose detection already initialized");
      return true;
    }

    try {
      console.log("🔄 Initializing AI pose detection...");

      // Check if MediaPipe is available
      if (typeof window === 'undefined') {
        throw new Error("Window not available");
      }

      // Add multiple CDN fallbacks
      const cdnUrls = [
        "https://cdn.jsdelivr.net/npm/@mediapipe/tasks-vision@0.10.0/wasm",
        "https://unpkg.com/@mediapipe/tasks-vision@0.10.0/wasm",
        "https://cdn.skypack.dev/@mediapipe/tasks-vision@0.10.0/wasm"
      ];

      let vision = null;
      let lastError = null;

      // Try each CDN
      for (const cdnUrl of cdnUrls) {
        try {
          console.log(`🔄 Trying CDN: ${cdnUrl}`);
          vision = await Promise.race([
            FilesetResolver.forVisionTasks(cdnUrl),
            new Promise((_, reject) =>
              setTimeout(() => reject(new Error("CDN timeout")), 5000)
            )
          ]);
          console.log(`✅ CDN loaded: ${cdnUrl}`);
          break;
        } catch (error) {
          console.warn(`❌ CDN failed: ${cdnUrl}`, error);
          lastError = error;
          continue;
        }
      }

      if (!vision) {
        throw lastError || new Error("All CDNs failed");
      }

      // Try different model configurations
      const modelConfigs = [
        {
          modelAssetPath: "https://storage.googleapis.com/mediapipe-models/pose_landmarker/pose_landmarker_lite/float16/1/pose_landmarker_lite.task",
          delegate: "GPU" as const
        },
        {
          modelAssetPath: "https://storage.googleapis.com/mediapipe-models/pose_landmarker/pose_landmarker_lite/float16/1/pose_landmarker_lite.task",
          delegate: "CPU" as const
        },
        {
          modelAssetPath: "https://storage.googleapis.com/mediapipe-models/pose_landmarker/pose_landmarker_heavy/float16/1/pose_landmarker_heavy.task",
          delegate: "CPU" as const
        }
      ];

      for (const config of modelConfigs) {
        try {
          console.log(`🔄 Trying model config: ${config.delegate}`);

          poseLandmarker.current = await Promise.race([
            PoseLandmarker.createFromOptions(vision, {
              baseOptions: config,
              runningMode: "VIDEO" as const,
              numPoses: 1
            }),
            new Promise((_, reject) =>
              setTimeout(() => reject(new Error("Model timeout")), 8000)
            )
          ]);

          console.log(`✅ Pose detection initialized with ${config.delegate}`);
          return true;
        } catch (error) {
          console.warn(`❌ Model config failed: ${config.delegate}`, error);
          continue;
        }
      }

      throw new Error("All model configurations failed");

    } catch (error) {
      console.error("❌ Failed to initialize pose detection:", error);
      poseLandmarker.current = null;
      return false;
    }
  }, []);

  const evaluateExercise = useCallback((detectedPoses: Pose[]) => {
    if (!detectedPoses.length || !currentExercise) return;

    const pose = detectedPoses[0];
    let evaluation;

    switch (currentExercise) {
      case 'squats':
        evaluation = evaluateSquats(pose, exerciseState.current);
        break;
      case 'pushups':
        evaluation = evaluatePushUps(pose, exerciseState.current);
        break;
      default:
        return;
    }

    setFeedback(evaluation.feedback);
    setIsCorrect(evaluation.isCorrect);
    setRepCount(evaluation.repCount);
    
    exerciseState.current = evaluation.state;
  }, [currentExercise]);

  const detectPose = useCallback(async () => {
    // Check if we have all required elements and pose detection is ready
    if (!poseLandmarker.current || !videoRef.current || !canvasRef.current) {
      if (animationId.current) {
        requestAnimationFrame(detectPose);
      }
      return;
    }

    const video = videoRef.current;
    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');

    // Make sure video is ready and has valid dimensions
    if (video.readyState >= 2 && video.videoWidth > 0 && video.videoHeight > 0) {
      try {
        const results = poseLandmarker.current.detectForVideo(video, performance.now());

        // Clear canvas
        ctx?.clearRect(0, 0, canvas.width, canvas.height);

        if (results.landmarks && results.landmarks.length > 0) {
          const detectedPoses: Pose[] = results.landmarks.map((landmarks, index) => ({
            landmarks,
            worldLandmarks: results.worldLandmarks?.[index] || landmarks
          }));

          setPoses(detectedPoses);
          evaluateExercise(detectedPoses);
        } else {
          // Clear poses if no detection
          setPoses([]);
        }
      } catch (error) {
        console.error("Pose detection error:", error);
        // Don't stop the animation loop on error
      }
    }

    if (animationId.current) {
      requestAnimationFrame(detectPose);
    }
  }, [videoRef, canvasRef, evaluateExercise]);

  const startExercise = useCallback(async (exercise: Exercise) => {
    try {
      console.log(`🏃‍♂️ Starting ${exercise} exercise...`);

      // Reset exercise state
      exerciseState.current = {
        phase: 'rest',
        repCount: 0,
        isCorrect: false,
        lastCorrectTime: 0
      };
      setRepCount(0);
      setFeedback({ message: `Initializing ${exercise}...`, type: 'info' });

      // Try to initialize pose detection
      const initialized = await initializePoseDetection();

      if (initialized) {
        setFeedback({ message: `${exercise} started! Get in position.`, type: 'success' });
        animationId.current = requestAnimationFrame(detectPose);
        console.log(`✅ ${exercise} exercise started successfully`);
      } else {
        // Fallback mode - still allow exercise without pose detection
        setFeedback({
          message: `${exercise} started in basic mode. Manual counting only.`,
          type: 'warning'
        });
        console.warn(`⚠️ ${exercise} started without pose detection`);

        // Start a simple timer for basic exercise mode
        const basicTimer = setInterval(() => {
          setFeedback({
            message: `Keep going! Count your ${exercise} manually.`,
            type: 'info'
          });
        }, 10000);

        // Store timer ID for cleanup
        animationId.current = basicTimer as any;
      }
    } catch (error) {
      console.error(`❌ Error starting ${exercise}:`, error);
      setFeedback({
        message: `Error starting ${exercise}. Please try again.`,
        type: 'error'
      });
    }
  }, [detectPose, initializePoseDetection]);

  const stopExercise = useCallback(() => {
    if (animationId.current) {
      // Handle both animation frames and timers
      if (typeof animationId.current === 'number') {
        if (animationId.current > 1000) {
          // Likely a timer ID
          clearInterval(animationId.current);
        } else {
          // Likely an animation frame ID
          cancelAnimationFrame(animationId.current);
        }
      }
      animationId.current = null;
    }
    setPoses([]);
    setFeedback(null);
    setIsCorrect(false);
    setRepCount(0);
    console.log('🛑 Exercise stopped');
  }, []);

  return {
    poses,
    feedback,
    isCorrect,
    repCount,
    initializePoseDetection,
    startExercise,
    stopExercise
  };
};
