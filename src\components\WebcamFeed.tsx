
import { forwardRef, useEffect, useState, useCallback, useImperativeHandle } from 'react';
import { Camera, CameraOff, AlertCircle, RefreshCw, Play, Square } from 'lucide-react';

interface WebcamFeedProps {
  className?: string;
  autoStart?: boolean;
  showControls?: boolean;
  onCameraStateChange?: (isRunning: boolean) => void;
}

export interface WebcamFeedRef {
  startCamera: () => Promise<void>;
  stopCamera: () => void;
  isRunning: boolean;
}

export const WebcamFeed = forwardRef<HTMLVideoElement, WebcamFeedProps>(
  ({ className = '', autoStart = true, showControls = false, onCameraStateChange }, ref) => {
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [isRunning, setIsRunning] = useState(false);
    const [permissionState, setPermissionState] = useState<'prompt' | 'granted' | 'denied'>('prompt');
    const [stream, setStream] = useState<MediaStream | null>(null);

    const checkCameraPermissions = useCallback(async () => {
      try {
        if ('permissions' in navigator) {
          const permission = await navigator.permissions.query({ name: 'camera' as PermissionName });
          setPermissionState(permission.state);
          console.log('Camera permission state:', permission.state);
          return permission.state === 'granted';
        }
        return true; // Assume granted if permissions API not available
      } catch (error) {
        console.log('Permissions API not available, proceeding with camera request');
        return true;
      }
    }, []);

    const startWebcam = useCallback(async () => {
      console.log('Starting webcam...');
      setIsLoading(true);
      setError(null);

      // Add timeout to prevent getting stuck
      const timeoutId = setTimeout(() => {
        setIsLoading(false);
        setError('Camera initialization timed out. Please try again or check your camera permissions.');
      }, 10000); // 10 second timeout

      try {
        // Stop any existing stream first
        if (stream) {
          stream.getTracks().forEach(track => track.stop());
          setStream(null);
        }

        // Check if getUserMedia is supported
        if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
          throw new Error('Camera access is not supported in this browser. Please use a modern browser like Chrome, Firefox, or Safari.');
        }

        // Check if we're in a secure context
        if (!window.isSecureContext) {
          console.warn('Not in secure context - camera may not work');
        }

        // Check permissions first
        await checkCameraPermissions();

        console.log('Requesting camera permissions...');

        // Try with ideal settings first
        let newStream: MediaStream;
        try {
          newStream = await navigator.mediaDevices.getUserMedia({
            video: {
              width: { ideal: 640, min: 320 },
              height: { ideal: 480, min: 240 },
              facingMode: 'user'
            }
          });
        } catch (idealError) {
          console.log('Ideal settings failed, trying basic settings:', idealError);
          // Fallback to basic settings
          newStream = await navigator.mediaDevices.getUserMedia({
            video: true
          });
        }

        console.log('Camera stream obtained:', newStream);
        setStream(newStream);
        setPermissionState('granted');

        if (ref && typeof ref === 'object' && ref.current) {
          console.log('Setting video source...');
          const video = ref.current;

          // Clear any existing srcObject first
          video.srcObject = null;

          // Set all required video attributes
          video.setAttribute('autoplay', 'true');
          video.setAttribute('playsinline', 'true');
          video.setAttribute('muted', 'true');
          video.autoplay = true;
          video.playsInline = true;
          video.muted = true;
          video.controls = false;

          // Set the stream
          video.srcObject = newStream;

          // Force immediate play attempt
          const playVideo = async () => {
            try {
              console.log('Attempting to play video...');
              await video.play();
              console.log('Video playing successfully');
              clearTimeout(timeoutId);
              setIsLoading(false);
              setIsRunning(true);
              onCameraStateChange?.(true);
            } catch (playError) {
              console.error('Play error:', playError);
              // Try again after a short delay
              setTimeout(async () => {
                try {
                  await video.play();
                  console.log('Video playing on retry');
                  clearTimeout(timeoutId);
                  setIsLoading(false);
                  setIsRunning(true);
                  onCameraStateChange?.(true);
                } catch (retryError) {
                  console.error('Retry play error:', retryError);
                  setError('Failed to start video playback. Please try refreshing the page.');
                  setIsLoading(false);
                }
              }, 500);
            }
          };

          const handleLoadedMetadata = () => {
            console.log('Video metadata loaded:', {
              videoWidth: video.videoWidth,
              videoHeight: video.videoHeight,
              readyState: video.readyState,
              paused: video.paused,
              srcObject: !!video.srcObject
            });
            playVideo();
          };

          const handleCanPlay = () => {
            console.log('Video can play');
            if (video.paused) {
              playVideo();
            }
          };

          const handleError = (err: any) => {
            console.error('Video element error:', err);
            clearTimeout(timeoutId);
            setError('Video playback error. Please check your camera.');
            setIsLoading(false);
          };

          // Add event listeners
          video.addEventListener('loadedmetadata', handleLoadedMetadata, { once: true });
          video.addEventListener('canplay', handleCanPlay, { once: true });
          video.addEventListener('error', handleError);

          // Also try to play immediately
          playVideo();

        } else {
          clearTimeout(timeoutId);
          setIsLoading(false);
          setIsRunning(true);
          onCameraStateChange?.(true);
        }
      } catch (err) {
        console.error('Error accessing webcam:', err);
        clearTimeout(timeoutId);
        let errorMessage = 'Unable to access camera. ';

        if (err instanceof Error) {
          if (err.name === 'NotAllowedError') {
            setPermissionState('denied');
            errorMessage += 'Camera permission denied. Please allow camera access and refresh the page.';
          } else if (err.name === 'NotFoundError') {
            errorMessage += 'No camera found. Please connect a camera and try again.';
          } else if (err.name === 'NotReadableError') {
            errorMessage += 'Camera is already in use by another application.';
          } else if (err.name === 'OverconstrainedError') {
            errorMessage += 'Camera settings not supported. Trying basic settings...';
          } else {
            errorMessage += err.message;
          }
        } else {
          errorMessage += 'Please check permissions and try again.';
        }

        setError(errorMessage);
        setIsLoading(false);
        setIsRunning(false);
        onCameraStateChange?.(false);
      }
    }, [ref, checkCameraPermissions, stream, onCameraStateChange]);

    const stopWebcam = useCallback(() => {
      console.log('Stopping webcam...');
      if (stream) {
        stream.getTracks().forEach(track => {
          console.log('Stopping track:', track.kind);
          track.stop();
        });
        setStream(null);
      }
      if (ref && typeof ref === 'object' && ref.current) {
        ref.current.srcObject = null;
      }
      setIsRunning(false);
      setError(null);
      onCameraStateChange?.(false);
    }, [stream, ref, onCameraStateChange]);

    useEffect(() => {
      if (autoStart) {
        startWebcam();
      }

      return () => {
        console.log('Cleaning up webcam...');
        if (stream) {
          stream.getTracks().forEach(track => track.stop());
        }
      };
    }, [autoStart]); // Remove startWebcam from dependencies to prevent infinite loop

    if (error) {
      return (
        <div className="w-full h-96 bg-gray-800 rounded-lg flex items-center justify-center">
          <div className="text-center p-6 max-w-md">
            <CameraOff className="w-16 h-16 text-red-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-white mb-2">Camera Access Issue</h3>
            <p className="text-red-400 text-sm mb-4">{error}</p>

            {permissionState === 'denied' && (
              <div className="bg-yellow-900/50 border border-yellow-600 rounded-lg p-3 mb-4">
                <AlertCircle className="w-5 h-5 text-yellow-400 mx-auto mb-2" />
                <p className="text-yellow-200 text-xs">
                  To enable camera access:
                  <br />1. Click the camera icon in your browser's address bar
                  <br />2. Select "Allow" for camera permissions
                  <br />3. Refresh this page
                </p>
              </div>
            )}

            <div className="flex gap-2 justify-center flex-wrap">
              <button
                onClick={startWebcam}
                disabled={isLoading}
                className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 disabled:opacity-50 flex items-center gap-2"
              >
                <Play className="w-4 h-4" />
                {isLoading ? 'Starting...' : 'Start Camera'}
              </button>
              <button
                onClick={() => window.location.reload()}
                className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700"
              >
                Refresh Page
              </button>
            </div>
          </div>
        </div>
      );
    }

    if (isLoading) {
      return (
        <div className="w-full h-96 bg-gray-800 rounded-lg flex items-center justify-center">
          <div className="text-center p-6">
            <Camera className="w-16 h-16 text-blue-400 mx-auto mb-4 animate-pulse" />
            <h3 className="text-lg font-semibold text-white mb-2">Initializing Camera</h3>
            <p className="text-gray-400 mb-2">Starting camera...</p>
            <p className="text-gray-500 text-sm">Please allow camera permissions if prompted</p>

            {!window.isSecureContext && (
              <div className="bg-orange-900/50 border border-orange-600 rounded-lg p-3 mt-4">
                <AlertCircle className="w-5 h-5 text-orange-400 mx-auto mb-2" />
                <p className="text-orange-200 text-xs">
                  For best camera support, use HTTPS or localhost
                </p>
              </div>
            )}
          </div>
        </div>
      );
    }

    if (!isRunning && !autoStart) {
      return (
        <div className="w-full h-96 bg-gray-800 rounded-lg flex items-center justify-center">
          <div className="text-center p-6">
            <Camera className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-white mb-2">Camera Ready</h3>
            <p className="text-gray-400 mb-4">Click start to begin camera feed</p>
            <button
              onClick={startWebcam}
              disabled={isLoading}
              className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 disabled:opacity-50 flex items-center gap-2 mx-auto"
            >
              <Play className="w-4 h-4" />
              Start Camera
            </button>
          </div>
        </div>
      );
    }

    return (
      <div className="relative">
        <video
          ref={ref}
          className={`w-full h-auto max-w-full bg-black rounded-lg ${className}`}
          autoPlay
          playsInline
          muted
          controls={false}
          style={{
            transform: 'scaleX(-1)',
            minHeight: '400px',
            maxHeight: '600px',
            objectFit: 'cover',
            display: 'block'
          }}
        />

        {showControls && (
          <div className="absolute top-4 right-4 flex gap-2">
            <button
              onClick={startWebcam}
              disabled={isLoading || isRunning}
              className="px-3 py-2 bg-green-600/80 text-white rounded hover:bg-green-700/80 disabled:opacity-50 flex items-center gap-1 text-sm backdrop-blur-sm"
            >
              <Play className="w-3 h-3" />
              Start
            </button>
            <button
              onClick={stopWebcam}
              disabled={!isRunning}
              className="px-3 py-2 bg-red-600/80 text-white rounded hover:bg-red-700/80 disabled:opacity-50 flex items-center gap-1 text-sm backdrop-blur-sm"
            >
              <Square className="w-3 h-3" />
              Stop
            </button>
            <button
              onClick={() => {
                if (ref && typeof ref === 'object' && ref.current) {
                  const video = ref.current;
                  console.log('Video debug info:', {
                    srcObject: !!video.srcObject,
                    videoWidth: video.videoWidth,
                    videoHeight: video.videoHeight,
                    readyState: video.readyState,
                    paused: video.paused,
                    currentTime: video.currentTime,
                    duration: video.duration
                  });
                  // Try to force play
                  video.play().catch(console.error);
                }
              }}
              className="px-2 py-2 bg-blue-600/80 text-white rounded hover:bg-blue-700/80 text-xs backdrop-blur-sm"
              title="Debug & Force Play"
            >
              🔧
            </button>
            <div className="flex items-center gap-1 px-2 py-1 bg-gray-800/80 rounded text-xs backdrop-blur-sm">
              <div className={`w-2 h-2 rounded-full ${isRunning ? 'bg-green-400 animate-pulse' : 'bg-gray-500'}`}></div>
              <span className="text-gray-300">{isRunning ? 'Live' : 'Off'}</span>
            </div>
          </div>
        )}
      </div>
    );
  }
);

WebcamFeed.displayName = 'WebcamFeed';
