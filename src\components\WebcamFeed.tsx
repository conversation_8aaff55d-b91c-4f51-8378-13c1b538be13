
import { forwardRef, useEffect, useState } from 'react';
import { Camera, CameraOff } from 'lucide-react';

interface WebcamFeedProps {
  className?: string;
}

export const WebcamFeed = forwardRef<HTMLVideoElement, WebcamFeedProps>(
  ({ className = '' }, ref) => {
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
      const startWebcam = async () => {
        console.log('Starting webcam...');
        try {
          // Check if getUserMedia is supported
          if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
            throw new Error('getUserMedia is not supported in this browser');
          }

          console.log('Requesting camera permissions...');
          const stream = await navigator.mediaDevices.getUserMedia({
            video: {
              width: { ideal: 640 },
              height: { ideal: 480 },
              facingMode: 'user'
            }
          });

          console.log('Camera stream obtained:', stream);

          if (ref && typeof ref === 'object' && ref.current) {
            console.log('Setting video source...');
            ref.current.srcObject = stream;
            
            ref.current.onloadedmetadata = () => {
              console.log('Video metadata loaded, starting playback...');
              setIsLoading(false);
              if (ref && typeof ref === 'object' && ref.current) {
                ref.current.play().catch(err => {
                  console.error('Error playing video:', err);
                  setError('Failed to start video playback');
                });
              }
            };

            ref.current.onerror = (err) => {
              console.error('Video element error:', err);
              setError('Video playback error');
            };
          }
        } catch (err) {
          console.error('Error accessing webcam:', err);
          let errorMessage = 'Unable to access camera. ';
          
          if (err instanceof Error) {
            if (err.name === 'NotAllowedError') {
              errorMessage += 'Please allow camera permissions.';
            } else if (err.name === 'NotFoundError') {
              errorMessage += 'No camera found.';
            } else if (err.name === 'NotReadableError') {
              errorMessage += 'Camera is already in use.';
            } else {
              errorMessage += err.message;
            }
          } else {
            errorMessage += 'Please check permissions and try again.';
          }
          
          setError(errorMessage);
          setIsLoading(false);
        }
      };

      startWebcam();

      return () => {
        console.log('Cleaning up webcam...');
        if (ref && typeof ref === 'object' && ref.current) {
          const stream = ref.current.srcObject as MediaStream;
          if (stream) {
            stream.getTracks().forEach(track => {
              console.log('Stopping track:', track.kind);
              track.stop();
            });
          }
          ref.current.srcObject = null;
        }
      };
    }, [ref]);

    if (error) {
      return (
        <div className="w-full h-96 bg-gray-800 rounded-lg flex items-center justify-center">
          <div className="text-center p-4">
            <CameraOff className="w-12 h-12 text-red-400 mx-auto mb-4" />
            <p className="text-red-400 text-sm">{error}</p>
            <button 
              onClick={() => window.location.reload()} 
              className="mt-4 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
            >
              Retry
            </button>
          </div>
        </div>
      );
    }

    if (isLoading) {
      return (
        <div className="w-full h-96 bg-gray-800 rounded-lg flex items-center justify-center">
          <div className="text-center">
            <Camera className="w-12 h-12 text-blue-400 mx-auto mb-4 animate-pulse" />
            <p className="text-gray-400">Starting camera...</p>
            <p className="text-gray-500 text-sm mt-2">Please allow camera permissions if prompted</p>
          </div>
        </div>
      );
    }

    return (
      <video
        ref={ref}
        className={`w-full h-auto max-w-full bg-gray-900 rounded-lg ${className}`}
        autoPlay
        playsInline
        muted
        style={{ transform: 'scaleX(-1)' }}
      />
    );
  }
);

WebcamFeed.displayName = 'WebcamFeed';
