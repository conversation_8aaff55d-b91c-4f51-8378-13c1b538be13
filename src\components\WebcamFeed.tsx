
import { forwardRef, useEffect, useState, useCallback } from 'react';
import { Camera, CameraOff, AlertCircle, RefreshCw } from 'lucide-react';

interface WebcamFeedProps {
  className?: string;
}

export const WebcamFeed = forwardRef<HTMLVideoElement, WebcamFeedProps>(
  ({ className = '' }, ref) => {
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [permissionState, setPermissionState] = useState<'prompt' | 'granted' | 'denied'>('prompt');

    const checkCameraPermissions = useCallback(async () => {
      try {
        if ('permissions' in navigator) {
          const permission = await navigator.permissions.query({ name: 'camera' as PermissionName });
          setPermissionState(permission.state);
          console.log('Camera permission state:', permission.state);
          return permission.state === 'granted';
        }
        return true; // Assume granted if permissions API not available
      } catch (error) {
        console.log('Permissions API not available, proceeding with camera request');
        return true;
      }
    }, []);

    const startWebcam = useCallback(async () => {
      console.log('Starting webcam...');
      setIsLoading(true);
      setError(null);

      try {
        // Check if getUserMedia is supported
        if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
          throw new Error('Camera access is not supported in this browser. Please use a modern browser like Chrome, Firefox, or Safari.');
        }

        // Check if we're in a secure context
        if (!window.isSecureContext) {
          console.warn('Not in secure context - camera may not work');
        }

        // Check permissions first
        await checkCameraPermissions();

        console.log('Requesting camera permissions...');

        // Try with ideal settings first
        let stream: MediaStream;
        try {
          stream = await navigator.mediaDevices.getUserMedia({
            video: {
              width: { ideal: 640, min: 320 },
              height: { ideal: 480, min: 240 },
              facingMode: 'user'
            }
          });
        } catch (idealError) {
          console.log('Ideal settings failed, trying basic settings:', idealError);
          // Fallback to basic settings
          stream = await navigator.mediaDevices.getUserMedia({
            video: true
          });
        }

        console.log('Camera stream obtained:', stream);
        setPermissionState('granted');

        if (ref && typeof ref === 'object' && ref.current) {
          console.log('Setting video source...');
          ref.current.srcObject = stream;

          ref.current.onloadedmetadata = () => {
            console.log('Video metadata loaded, starting playback...');
            setIsLoading(false);
            if (ref && typeof ref === 'object' && ref.current) {
              ref.current.play().catch(err => {
                console.error('Error playing video:', err);
                setError('Failed to start video playback. Please refresh the page.');
              });
            }
          };

          ref.current.onerror = (err) => {
            console.error('Video element error:', err);
            setError('Video playback error. Please check your camera.');
          };
        }
      } catch (err) {
        console.error('Error accessing webcam:', err);
        let errorMessage = 'Unable to access camera. ';

        if (err instanceof Error) {
          if (err.name === 'NotAllowedError') {
            setPermissionState('denied');
            errorMessage += 'Camera permission denied. Please allow camera access and refresh the page.';
          } else if (err.name === 'NotFoundError') {
            errorMessage += 'No camera found. Please connect a camera and try again.';
          } else if (err.name === 'NotReadableError') {
            errorMessage += 'Camera is already in use by another application.';
          } else if (err.name === 'OverconstrainedError') {
            errorMessage += 'Camera settings not supported. Trying basic settings...';
          } else {
            errorMessage += err.message;
          }
        } else {
          errorMessage += 'Please check permissions and try again.';
        }

        setError(errorMessage);
        setIsLoading(false);
      }
    }, [ref, checkCameraPermissions]);

    useEffect(() => {
      startWebcam();

      return () => {
        console.log('Cleaning up webcam...');
        if (ref && typeof ref === 'object' && ref.current) {
          const stream = ref.current.srcObject as MediaStream;
          if (stream) {
            stream.getTracks().forEach(track => {
              console.log('Stopping track:', track.kind);
              track.stop();
            });
          }
          ref.current.srcObject = null;
        }
      };
    }, [startWebcam]);

    if (error) {
      return (
        <div className="w-full h-96 bg-gray-800 rounded-lg flex items-center justify-center">
          <div className="text-center p-6 max-w-md">
            <CameraOff className="w-16 h-16 text-red-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-white mb-2">Camera Access Issue</h3>
            <p className="text-red-400 text-sm mb-4">{error}</p>

            {permissionState === 'denied' && (
              <div className="bg-yellow-900/50 border border-yellow-600 rounded-lg p-3 mb-4">
                <AlertCircle className="w-5 h-5 text-yellow-400 mx-auto mb-2" />
                <p className="text-yellow-200 text-xs">
                  To enable camera access:
                  <br />1. Click the camera icon in your browser's address bar
                  <br />2. Select "Allow" for camera permissions
                  <br />3. Refresh this page
                </p>
              </div>
            )}

            <div className="flex gap-2 justify-center">
              <button
                onClick={startWebcam}
                className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 flex items-center gap-2"
              >
                <RefreshCw className="w-4 h-4" />
                Try Again
              </button>
              <button
                onClick={() => window.location.reload()}
                className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700"
              >
                Refresh Page
              </button>
            </div>
          </div>
        </div>
      );
    }

    if (isLoading) {
      return (
        <div className="w-full h-96 bg-gray-800 rounded-lg flex items-center justify-center">
          <div className="text-center p-6">
            <Camera className="w-16 h-16 text-blue-400 mx-auto mb-4 animate-pulse" />
            <h3 className="text-lg font-semibold text-white mb-2">Initializing Camera</h3>
            <p className="text-gray-400 mb-2">Starting camera...</p>
            <p className="text-gray-500 text-sm">Please allow camera permissions if prompted</p>

            {!window.isSecureContext && (
              <div className="bg-orange-900/50 border border-orange-600 rounded-lg p-3 mt-4">
                <AlertCircle className="w-5 h-5 text-orange-400 mx-auto mb-2" />
                <p className="text-orange-200 text-xs">
                  For best camera support, use HTTPS or localhost
                </p>
              </div>
            )}
          </div>
        </div>
      );
    }

    return (
      <video
        ref={ref}
        className={`w-full h-auto max-w-full bg-gray-900 rounded-lg ${className}`}
        autoPlay
        playsInline
        muted
        style={{ transform: 'scaleX(-1)' }}
      />
    );
  }
);

WebcamFeed.displayName = 'WebcamFeed';
