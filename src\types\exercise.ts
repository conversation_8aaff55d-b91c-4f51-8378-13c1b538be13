
export type Exercise = 'squats' | 'pushups';

export interface PoseLandmark {
  x: number;
  y: number;
  z: number;
  visibility?: number;
}

export interface Pose {
  landmarks: PoseLandmark[];
  worldLandmarks: PoseLandmark[];
}

export interface ExerciseFeedback {
  message: string;
  type: 'error' | 'success' | 'warning';
  details?: string[];
}

export interface ExerciseState {
  phase: 'down' | 'up' | 'rest';
  repCount: number;
  isCorrect: boolean;
  lastCorrectTime: number;
}
