import { useState, useRef, useEffect } from 'react';
import { Camera, CameraOff, CheckCircle, AlertCircle, Info, Play, Square } from 'lucide-react';

export const CameraTest = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [isRunning, setIsRunning] = useState(false);
  const [devices, setDevices] = useState<MediaDeviceInfo[]>([]);
  const [selectedDevice, setSelectedDevice] = useState<string>('');
  const videoRef = useRef<HTMLVideoElement>(null);
  const streamRef = useRef<MediaStream | null>(null);

  const getDevices = async () => {
    try {
      const deviceList = await navigator.mediaDevices.enumerateDevices();
      const videoDevices = deviceList.filter(device => device.kind === 'videoinput');
      setDevices(videoDevices);
      if (videoDevices.length > 0 && !selectedDevice) {
        setSelectedDevice(videoDevices[0].deviceId);
      }
    } catch (error) {
      console.error('Error getting devices:', error);
    }
  };

  const startCamera = async () => {
    setIsLoading(true);
    setError(null);
    setSuccess(false);

    try {
      // Stop any existing stream
      if (streamRef.current) {
        streamRef.current.getTracks().forEach(track => track.stop());
      }

      const constraints: MediaStreamConstraints = {
        video: selectedDevice ? { deviceId: selectedDevice } : true
      };

      console.log('Starting camera with constraints:', constraints);
      const stream = await navigator.mediaDevices.getUserMedia(constraints);

      streamRef.current = stream;

      if (videoRef.current) {
        videoRef.current.srcObject = stream;
        videoRef.current.onloadedmetadata = () => {
          setIsLoading(false);
          setSuccess(true);
          setIsRunning(true);
          console.log('Camera started successfully!');
        };
      }
    } catch (err) {
      console.error('Camera start failed:', err);
      setIsLoading(false);
      setIsRunning(false);

      let errorMessage = 'Camera start failed: ';
      if (err instanceof Error) {
        switch (err.name) {
          case 'NotAllowedError':
            errorMessage += 'Permission denied. Please allow camera access.';
            break;
          case 'NotFoundError':
            errorMessage += 'No camera found.';
            break;
          case 'NotReadableError':
            errorMessage += 'Camera is in use by another application.';
            break;
          case 'OverconstrainedError':
            errorMessage += 'Camera constraints not supported.';
            break;
          default:
            errorMessage += err.message;
        }
      }
      setError(errorMessage);
    }
  };

  const stopCamera = () => {
    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop());
      streamRef.current = null;
    }
    if (videoRef.current) {
      videoRef.current.srcObject = null;
    }
    setSuccess(false);
    setIsRunning(false);
    setError(null);
    console.log('Camera stopped');
  };

  useEffect(() => {
    getDevices();
    
    return () => {
      stopCamera();
    };
  }, []);

  return (
    <div className="max-w-2xl mx-auto p-6 bg-gray-800 rounded-lg">
      <h2 className="text-2xl font-bold text-white mb-6 flex items-center gap-2">
        <Camera className="w-6 h-6" />
        Camera Test
      </h2>

      {/* System Info */}
      <div className="mb-6 p-4 bg-gray-700 rounded-lg">
        <h3 className="text-lg font-semibold text-white mb-2 flex items-center gap-2">
          <Info className="w-5 h-5" />
          System Information
        </h3>
        <div className="text-sm text-gray-300 space-y-1">
          <p>Secure Context: {window.isSecureContext ? '✅ Yes' : '❌ No'}</p>
          <p>getUserMedia Support: {navigator.mediaDevices?.getUserMedia ? '✅ Yes' : '❌ No'}</p>
          <p>Protocol: {window.location.protocol}</p>
          <p>Host: {window.location.host}</p>
        </div>
      </div>

      {/* Device Selection */}
      {devices.length > 0 && (
        <div className="mb-4">
          <label className="block text-white text-sm font-medium mb-2">
            Select Camera:
          </label>
          <select
            value={selectedDevice}
            onChange={(e) => setSelectedDevice(e.target.value)}
            className="w-full p-2 bg-gray-700 text-white rounded border border-gray-600"
          >
            {devices.map((device) => (
              <option key={device.deviceId} value={device.deviceId}>
                {device.label || `Camera ${device.deviceId.slice(0, 8)}...`}
              </option>
            ))}
          </select>
        </div>
      )}

      {/* Controls */}
      <div className="flex gap-4 mb-6 flex-wrap">
        <button
          onClick={startCamera}
          disabled={isLoading || isRunning}
          className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 disabled:opacity-50 flex items-center gap-2"
        >
          <Play className="w-4 h-4" />
          {isLoading ? 'Starting...' : 'Start Camera'}
        </button>

        <button
          onClick={stopCamera}
          disabled={!isRunning}
          className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 disabled:opacity-50 flex items-center gap-2"
        >
          <Square className="w-4 h-4" />
          Stop Camera
        </button>

        {/* Camera Status Indicator */}
        <div className="flex items-center gap-2 px-3 py-2 bg-gray-700 rounded">
          <div className={`w-3 h-3 rounded-full ${isRunning ? 'bg-green-400 animate-pulse' : 'bg-gray-500'}`}></div>
          <span className="text-sm text-gray-300">
            {isRunning ? 'Camera Running' : 'Camera Stopped'}
          </span>
        </div>
      </div>

      {/* Status */}
      {error && (
        <div className="mb-4 p-4 bg-red-900/50 border border-red-600 rounded-lg flex items-start gap-2">
          <AlertCircle className="w-5 h-5 text-red-400 mt-0.5" />
          <p className="text-red-200 text-sm">{error}</p>
        </div>
      )}

      {success && isRunning && (
        <div className="mb-4 p-4 bg-green-900/50 border border-green-600 rounded-lg flex items-center gap-2">
          <CheckCircle className="w-5 h-5 text-green-400" />
          <p className="text-green-200 text-sm">Camera is working correctly and streaming!</p>
        </div>
      )}

      {/* Video Preview */}
      <div className="relative bg-gray-900 rounded-lg overflow-hidden">
        <video
          ref={videoRef}
          autoPlay
          playsInline
          muted
          className="w-full h-64 object-cover"
          style={{ transform: 'scaleX(-1)' }}
        />
        {!isRunning && !isLoading && (
          <div className="absolute inset-0 flex items-center justify-center bg-gray-900">
            <div className="text-center">
              <Camera className="w-12 h-12 text-gray-500 mx-auto mb-2" />
              <p className="text-gray-400">Click "Start Camera" to begin</p>
            </div>
          </div>
        )}
        {isLoading && (
          <div className="absolute inset-0 flex items-center justify-center bg-gray-900">
            <div className="text-center">
              <Camera className="w-12 h-12 text-blue-400 mx-auto mb-2 animate-pulse" />
              <p className="text-blue-400">Starting camera...</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
