
import { Text } from '@react-three/drei';
import { ExerciseFeedback } from '../types/exercise';

interface FeedbackArrowsProps {
  feedback: ExerciseFeedback;
}

export const FeedbackArrows = ({ feedback }: FeedbackArrowsProps) => {
  const getArrowDirection = () => {
    const message = feedback.message.toLowerCase();
    
    if (message.includes('lower') || message.includes('deeper')) {
      return { position: [0, 0.5, 0] as [number, number, number], rotation: [0, 0, Math.PI] as [number, number, number] };
    }
    if (message.includes('up') || message.includes('higher')) {
      return { position: [0, -0.5, 0] as [number, number, number], rotation: [0, 0, 0] as [number, number, number] };
    }
    if (message.includes('straight') || message.includes('align')) {
      return { position: [0, 0, 0] as [number, number, number], rotation: [0, 0, 0] as [number, number, number] };
    }
    
    return { position: [0, 0, 0] as [number, number, number], rotation: [0, 0, 0] as [number, number, number] };
  };

  const arrow = getArrowDirection();

  return (
    <group>
      {/* Arrow */}
      <mesh position={arrow.position} rotation={arrow.rotation}>
        <coneGeometry args={[0.1, 0.3, 8]} />
        <meshStandardMaterial color="#fbbf24" />
      </mesh>
      
      {/* Feedback Text */}
      <Text
        position={[0, 1.5, 0]}
        fontSize={0.2}
        color="#fbbf24"
        anchorX="center"
        anchorY="middle"
        maxWidth={3}
      >
        {feedback.message}
      </Text>
    </group>
  );
};
