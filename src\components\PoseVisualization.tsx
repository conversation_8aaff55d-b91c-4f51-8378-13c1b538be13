
import { useRef } from 'react';
import { useFrame } from '@react-three/fiber';
import { Sphere, Line } from '@react-three/drei';
import { Group } from 'three';
import { Pose } from '../types/exercise';
import { getPoseConnections, getLandmarkPosition } from '../utils/poseUtils';

interface PoseVisualizationProps {
  pose: Pose;
  isCorrect: boolean;
}

export const PoseVisualization = ({ pose, isCorrect }: PoseVisualizationProps) => {
  const groupRef = useRef<Group>(null);
  const connections = getPoseConnections();
  
  useFrame((state) => {
    if (groupRef.current && !isCorrect) {
      groupRef.current.rotation.y = Math.sin(state.clock.elapsedTime) * 0.1;
    }
  });

  const color = isCorrect ? '#10b981' : '#ef4444';
  
  return (
    <group ref={groupRef} scale={[3, 3, 3]} position={[0, -1, 0]}>
      {/* Draw pose landmarks */}
      {pose.worldLandmarks.map((landmark, index) => {
        if (landmark.visibility && landmark.visibility < 0.5) return null;
        
        const position = getLandmarkPosition(landmark);
        return (
          <Sphere
            key={index}
            position={position}
            args={[0.02]}
          >
            <meshStandardMaterial color={color} />
          </Sphere>
        );
      })}
      
      {/* Draw pose connections */}
      {connections.map((connection, index) => {
        const [start, end] = connection;
        const startLandmark = pose.worldLandmarks[start];
        const endLandmark = pose.worldLandmarks[end];
        
        if (!startLandmark || !endLandmark) return null;
        if (startLandmark.visibility && startLandmark.visibility < 0.5) return null;
        if (endLandmark.visibility && endLandmark.visibility < 0.5) return null;
        
        const startPos = getLandmarkPosition(startLandmark);
        const endPos = getLandmarkPosition(endLandmark);
        
        return (
          <Line
            key={index}
            points={[startPos, endPos]}
            color={color}
            lineWidth={3}
          />
        );
      })}
    </group>
  );
};
