
import { useState, useRef, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Setting<PERSON>, Camera, CameraOff } from 'lucide-react';
import { WebcamFeed } from '../components/WebcamFeed';
import { SimpleCamera } from '../components/SimpleCamera';
import { BasicCamera } from '../components/BasicCamera';
import { WorkingCamera } from '../components/WorkingCamera';
import { ExerciseControls } from '../components/ExerciseControls';
import { PoseCanvas } from '../components/PoseCanvas';
import { FeedbackDisplay } from '../components/FeedbackDisplay';
import { Scene3D } from '../components/Scene3D';
import { usePoseDetection } from '../hooks/usePoseDetection';
import { useSimpleExercise } from '../hooks/useSimpleExercise';
import { useHybridExercise } from '../hooks/useHybridExercise';
import { Exercise } from '../types/exercise';

const Index = () => {
  const [currentExercise, setCurrentExercise] = useState<Exercise | null>(null);
  const [isActive, setIsActive] = useState(false);
  const [isCameraRunning, setIsCameraRunning] = useState(false);
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  
  // Use hybrid system that combines AI and manual modes
  const {
    aiMode,
    isActive,
    currentExercise: hybridCurrentExercise,
    poses,
    feedback,
    isCorrect,
    repCount,
    startExercise: hybridStartExercise,
    stopExercise: hybridStopExercise,
    toggleMode,
    initializeAI,
    incrementRep,
    decrementRep
  } = useHybridExercise(videoRef, canvasRef);

  // Disable pose detection initialization for now to prevent crashes
  // useEffect(() => {
  //   // Only initialize pose detection when camera is running
  //   if (isCameraRunning) {
  //     // Add a small delay to ensure camera is fully ready
  //     const timer = setTimeout(() => {
  //       initializePoseDetection();
  //     }, 1000);

  //     return () => clearTimeout(timer);
  //   }
  // }, [isCameraRunning, initializePoseDetection]);

  const handleStartExercise = async (exercise: Exercise) => {
    try {
      console.log(`🎯 User clicked start ${exercise} (hybrid mode)`);
      setCurrentExercise(exercise);
      await hybridStartExercise(exercise);
    } catch (error) {
      console.error(`❌ Error in handleStartExercise:`, error);
      setCurrentExercise(null);
    }
  };

  const handleStopExercise = () => {
    setCurrentExercise(null);
    hybridStopExercise();
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-gray-900 text-white">
      <div className="container mx-auto px-4 py-6">
        {/* Header */}
        <div className="text-center mb-8 relative">
          <h1 className="text-4xl md:text-6xl font-bold bg-gradient-to-r from-blue-400 to-emerald-400 bg-clip-text text-transparent mb-4">
            AI Fitness Trainer
          </h1>
          <p className="text-lg text-gray-300 max-w-2xl mx-auto">
            Real-time pose analysis and form correction powered by AI
          </p>

          {/* Camera Test Link and Status */}
          <div className="absolute top-0 right-0 flex gap-2">
            {/* AI Mode Status */}
            <div className="flex items-center gap-2 px-3 py-2 bg-gray-800 rounded-lg text-sm">
              <div className={`w-3 h-3 rounded-full ${
                aiMode === 'enabled' ? 'bg-blue-400 animate-pulse' :
                aiMode === 'loading' ? 'bg-yellow-400 animate-spin' :
                'bg-gray-500'
              }`}></div>
              <span className="text-gray-300">
                {aiMode === 'enabled' ? 'AI Active' :
                 aiMode === 'loading' ? 'AI Loading' :
                 'Manual Mode'}
              </span>
              <button
                onClick={toggleMode}
                className="ml-2 px-2 py-1 bg-blue-600 hover:bg-blue-700 text-white rounded text-xs transition-colors"
                disabled={aiMode === 'loading'}
              >
                {aiMode === 'enabled' ? 'Manual' : 'AI'}
              </button>
            </div>

            {/* Camera Status */}
            <div className="flex items-center gap-2 px-3 py-2 bg-gray-800 rounded-lg text-sm">
              <div className={`w-3 h-3 rounded-full ${isCameraRunning ? 'bg-green-400 animate-pulse' : 'bg-gray-500'}`}></div>
              <span className="text-gray-300">
                {isCameraRunning ? 'Camera Live' : 'Camera Off'}
              </span>
            </div>

            <Link
              to="/camera-test"
              className="flex items-center gap-2 px-3 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg text-sm transition-colors"
              title="Camera Diagnostics"
            >
              <Settings className="w-4 h-4" />
              Camera Test
            </Link>
          </div>
        </div>

        {/* AI Mode Banner */}
        {aiMode === 'disabled' && !isActive && (
          <div className="bg-gradient-to-r from-blue-600/20 to-purple-600/20 border border-blue-500/30 rounded-xl p-4 mb-6 text-center">
            <h3 className="text-lg font-semibold text-white mb-2">🤖 Try AI-Powered Form Analysis!</h3>
            <p className="text-gray-300 mb-3">Get real-time feedback on your exercise form with AI pose detection</p>
            <button
              onClick={initializeAI}
              className="px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-semibold transition-colors"
            >
              Enable AI Mode
            </button>
          </div>
        )}

        {/* Exercise Controls */}
        <ExerciseControls
          onStartSquats={() => handleStartExercise('squats')}
          onStartPushUps={() => handleStartExercise('pushups')}
          onStop={handleStopExercise}
          isActive={isActive}
          currentExercise={hybridCurrentExercise}
        />

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mt-8">
          {/* Video Feed and Pose Canvas */}
          <div className="lg:col-span-2 space-y-4">
            <div className="relative bg-gray-800 rounded-xl overflow-hidden shadow-2xl">
              <WorkingCamera
                ref={videoRef}
                onCameraStateChange={setIsCameraRunning}
              />
              {/* Pose canvas shown when AI is active */}
              {aiMode === 'enabled' && (
                <PoseCanvas
                  ref={canvasRef}
                  poses={poses}
                  isCorrect={isCorrect}
                  width={640}
                  height={480}
                />
              )}
            </div>
            
            {/* Stats and Manual Counter */}
            {isActive && (
              <div className="bg-gray-800/50 backdrop-blur-sm rounded-xl p-6">
                <div className="grid grid-cols-1 gap-4 text-center">
                  <div>
                    <div className="text-5xl font-bold text-blue-400 mb-2">{repCount}</div>
                    <div className="text-gray-400 mb-4">Reps</div>

                    {/* Show form status in AI mode */}
                    {aiMode === 'enabled' && (
                      <div className="mb-4">
                        <div className={`text-2xl font-bold ${isCorrect ? 'text-green-400' : 'text-red-400'}`}>
                          {isCorrect ? '✓ GOOD FORM' : '⚠ ADJUST FORM'}
                        </div>
                      </div>
                    )}

                    {/* Manual Rep Counter (only in manual mode) */}
                    {aiMode === 'disabled' && (
                      <div className="flex gap-3 justify-center">
                        <button
                          onClick={decrementRep}
                          className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg font-semibold transition-colors"
                        >
                          -1
                        </button>
                        <button
                          onClick={incrementRep}
                          className="px-6 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg font-semibold transition-colors"
                        >
                          +1 Rep
                        </button>
                      </div>
                    )}

                    {/* AI Mode indicator */}
                    {aiMode === 'enabled' && (
                      <div className="text-sm text-blue-300 mt-2">
                        🤖 AI is tracking your reps automatically
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Feedback and 3D Scene */}
          <div className="space-y-4">
            <FeedbackDisplay
              feedback={feedback}
              isCorrect={isCorrect}
              currentExercise={hybridCurrentExercise}
            />

            {isActive && (
              <div className="bg-gray-800 rounded-xl p-6 shadow-2xl">
                <h3 className="text-xl font-semibold text-white mb-4">
                  {aiMode === 'enabled' ? 'AI Analysis' : 'Exercise Tips'}
                </h3>
                <div className="space-y-3 text-gray-300">
                  {aiMode === 'enabled' ? (
                    <div>
                      <p className="text-blue-300 mb-2">🤖 AI is analyzing your form in real-time</p>
                      <p>• Pose detection: {poses.length > 0 ? '✅ Active' : '⏳ Waiting'}</p>
                      <p>• Form analysis: {isCorrect ? '✅ Good' : '⚠️ Needs adjustment'}</p>
                      <p>• Rep counting: Automatic</p>
                    </div>
                  ) : (
                    <div>
                      {hybridCurrentExercise === 'squats' && (
                        <>
                          <p>• Keep your feet shoulder-width apart</p>
                          <p>• Lower until thighs are parallel to floor</p>
                          <p>• Keep your chest up and core engaged</p>
                          <p>• Push through your heels to stand</p>
                        </>
                      )}
                      {hybridCurrentExercise === 'pushups' && (
                        <>
                          <p>• Keep your body in a straight line</p>
                          <p>• Lower until chest nearly touches floor</p>
                          <p>• Keep core tight throughout movement</p>
                          <p>• Push up explosively but controlled</p>
                        </>
                      )}
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Index;
