
import { useState, useRef, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Setting<PERSON>, Camera, CameraOff } from 'lucide-react';
import { WebcamFeed } from '../components/WebcamFeed';
import { SimpleCamera } from '../components/SimpleCamera';
import { BasicCamera } from '../components/BasicCamera';
import { ExerciseControls } from '../components/ExerciseControls';
import { PoseCanvas } from '../components/PoseCanvas';
import { FeedbackDisplay } from '../components/FeedbackDisplay';
import { Scene3D } from '../components/Scene3D';
import { usePoseDetection } from '../hooks/usePoseDetection';
import { Exercise } from '../types/exercise';

const Index = () => {
  const [currentExercise, setCurrentExercise] = useState<Exercise | null>(null);
  const [isActive, setIsActive] = useState(false);
  const [isCameraRunning, setIsCameraRunning] = useState(false);
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  
  const {
    poses,
    feedback,
    isCorrect,
    repCount,
    initializePoseDetection,
    startExercise,
    stopExercise
  } = usePoseDetection(videoRef, canvasRef, currentExercise);

  useEffect(() => {
    // Only initialize pose detection when camera is running
    if (isCameraRunning) {
      // Add a small delay to ensure camera is fully ready
      const timer = setTimeout(() => {
        initializePoseDetection();
      }, 1000);

      return () => clearTimeout(timer);
    }
  }, [isCameraRunning, initializePoseDetection]);

  const handleStartExercise = async (exercise: Exercise) => {
    setCurrentExercise(exercise);
    setIsActive(true);
    await startExercise(exercise);
  };

  const handleStopExercise = () => {
    setIsActive(false);
    setCurrentExercise(null);
    stopExercise();
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-gray-900 text-white">
      <div className="container mx-auto px-4 py-6">
        {/* Header */}
        <div className="text-center mb-8 relative">
          <h1 className="text-4xl md:text-6xl font-bold bg-gradient-to-r from-blue-400 to-emerald-400 bg-clip-text text-transparent mb-4">
            AI Fitness Trainer
          </h1>
          <p className="text-lg text-gray-300 max-w-2xl mx-auto">
            Real-time pose analysis and form correction powered by AI
          </p>

          {/* Camera Test Link and Status */}
          <div className="absolute top-0 right-0 flex gap-2">
            <div className="flex items-center gap-2 px-3 py-2 bg-gray-800 rounded-lg text-sm">
              <div className={`w-3 h-3 rounded-full ${isCameraRunning ? 'bg-green-400 animate-pulse' : 'bg-gray-500'}`}></div>
              <span className="text-gray-300">
                {isCameraRunning ? 'Camera Live' : 'Camera Off'}
              </span>
            </div>
            <Link
              to="/camera-test"
              className="flex items-center gap-2 px-3 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg text-sm transition-colors"
              title="Camera Diagnostics"
            >
              <Settings className="w-4 h-4" />
              Camera Test
            </Link>
          </div>
        </div>

        {/* Exercise Controls */}
        <ExerciseControls
          onStartSquats={() => handleStartExercise('squats')}
          onStartPushUps={() => handleStartExercise('pushups')}
          onStop={handleStopExercise}
          isActive={isActive}
          currentExercise={currentExercise}
        />

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mt-8">
          {/* Video Feed and Pose Canvas */}
          <div className="lg:col-span-2 space-y-4">
            <div className="relative bg-gray-800 rounded-xl overflow-hidden shadow-2xl">
              <WebcamFeed
                ref={videoRef}
                autoStart={false}
                showControls={true}
                onCameraStateChange={setIsCameraRunning}
              />
              <PoseCanvas
                ref={canvasRef}
                poses={poses}
                isCorrect={isCorrect}
                width={640}
                height={480}
              />
            </div>
            
            {/* Stats */}
            {isActive && (
              <div className="bg-gray-800/50 backdrop-blur-sm rounded-xl p-6">
                <div className="grid grid-cols-2 gap-4 text-center">
                  <div>
                    <div className="text-3xl font-bold text-blue-400">{repCount}</div>
                    <div className="text-gray-400">Reps</div>
                  </div>
                  <div>
                    <div className={`text-3xl font-bold ${isCorrect ? 'text-green-400' : 'text-red-400'}`}>
                      {isCorrect ? 'GOOD' : 'ADJUST'}
                    </div>
                    <div className="text-gray-400">Form</div>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Feedback and 3D Scene */}
          <div className="space-y-4">
            <FeedbackDisplay
              feedback={feedback}
              isCorrect={isCorrect}
              currentExercise={currentExercise}
            />
            
            {isActive && (
              <div className="bg-gray-800 rounded-xl overflow-hidden shadow-2xl h-80">
                <Scene3D
                  poses={poses}
                  feedback={feedback}
                  isCorrect={isCorrect}
                />
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Index;
