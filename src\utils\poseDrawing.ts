
import { Pose } from '../types/exercise';

export const drawPose = (
  ctx: CanvasRenderingContext2D,
  pose: Pose,
  isCorrect: boolean,
  width: number,
  height: number
) => {
  const color = isCorrect ? '#10b981' : '#ef4444';
  const connections = getPoseConnections();
  
  // Draw connections
  ctx.strokeStyle = color;
  ctx.lineWidth = 3;
  ctx.globalAlpha = 0.8;
  
  connections.forEach(([start, end]) => {
    const startLandmark = pose.landmarks[start];
    const endLandmark = pose.landmarks[end];
    
    if (!startLandmark || !endLandmark) return;
    if (startLandmark.visibility && startLandmark.visibility < 0.5) return;
    if (endLandmark.visibility && endLandmark.visibility < 0.5) return;
    
    ctx.beginPath();
    ctx.moveTo(startLandmark.x * width, startLandmark.y * height);
    ctx.lineTo(endLandmark.x * width, endLandmark.y * height);
    ctx.stroke();
  });
  
  // Draw landmarks
  ctx.fillStyle = color;
  ctx.globalAlpha = 1;
  
  pose.landmarks.forEach((landmark) => {
    if (landmark.visibility && landmark.visibility < 0.5) return;
    
    ctx.beginPath();
    ctx.arc(
      landmark.x * width,
      landmark.y * height,
      5,
      0,
      2 * Math.PI
    );
    ctx.fill();
  });
};

const getPoseConnections = (): [number, number][] => {
  return [
    // Face
    [0, 1], [1, 2], [2, 3], [3, 7],
    [0, 4], [4, 5], [5, 6], [6, 8],
    
    // Arms
    [9, 10], [11, 12], [11, 13], [13, 15], [15, 17], [15, 19], [15, 21],
    [12, 14], [14, 16], [16, 18], [16, 20], [16, 22],
    
    // Body
    [11, 23], [12, 24], [23, 24],
    
    // Legs
    [23, 25], [25, 27], [27, 29], [29, 31],
    [24, 26], [26, 28], [28, 30], [30, 32],
    [27, 29], [28, 30]
  ];
};
