import { useState, useCallback, useRef } from 'react';
import { Exercise, ExerciseFeedback } from '../types/exercise';
import { usePoseDetection } from './usePoseDetection';
import { useSimpleExercise } from './useSimpleExercise';

export const useHybridExercise = (
  videoRef: React.RefObject<HTMLVideoElement>,
  canvasRef: React.RefObject<HTMLCanvasElement>
) => {
  const [aiMode, setAiMode] = useState<'loading' | 'enabled' | 'disabled'>('disabled');
  const [currentExercise, setCurrentExercise] = useState<Exercise | null>(null);
  
  // AI pose detection
  const {
    poses,
    feedback: aiFeedback,
    isCorrect,
    repCount: aiRepCount,
    initializePoseDetection,
    startExercise: startAiExercise,
    stopExercise: stopAiExercise
  } = usePoseDetection(videoRef, canvasRef, currentExercise);

  // Simple manual mode
  const {
    feedback: simpleFeedback,
    repCount: simpleRepCount,
    isActive: simpleIsActive,
    currentExercise: simpleCurrentExercise,
    startExercise: startSimpleExercise,
    stopExercise: stopSimpleExercise,
    incrementRep,
    decrementRep
  } = useSimpleExercise();

  const initializeAI = useCallback(async () => {
    setAiMode('loading');
    console.log('🤖 Attempting to initialize AI...');
    
    try {
      const success = await initializePoseDetection();
      if (success) {
        setAiMode('enabled');
        console.log('✅ AI mode enabled');
        return true;
      } else {
        setAiMode('disabled');
        console.log('⚠️ AI initialization failed, using manual mode');
        return false;
      }
    } catch (error) {
      console.error('❌ AI initialization error:', error);
      setAiMode('disabled');
      return false;
    }
  }, [initializePoseDetection]);

  const startExercise = useCallback(async (exercise: Exercise) => {
    setCurrentExercise(exercise);
    
    if (aiMode === 'disabled') {
      // Try to initialize AI first
      console.log('🔄 Trying to enable AI for this session...');
      const aiEnabled = await initializeAI();
      
      if (aiEnabled) {
        // Start with AI
        console.log('🤖 Starting exercise with AI');
        await startAiExercise(exercise);
      } else {
        // Fallback to manual
        console.log('📱 Starting exercise in manual mode');
        startSimpleExercise(exercise);
      }
    } else if (aiMode === 'enabled') {
      // AI is ready, use it
      console.log('🤖 Starting exercise with AI (already enabled)');
      await startAiExercise(exercise);
    } else {
      // AI is loading, wait a bit then fallback
      console.log('⏳ AI is loading, waiting...');
      setTimeout(async () => {
        if (aiMode === 'enabled') {
          await startAiExercise(exercise);
        } else {
          startSimpleExercise(exercise);
        }
      }, 2000);
    }
  }, [aiMode, initializeAI, startAiExercise, startSimpleExercise]);

  const stopExercise = useCallback(() => {
    if (aiMode === 'enabled') {
      stopAiExercise();
    } else {
      stopSimpleExercise();
    }
    setCurrentExercise(null);
  }, [aiMode, stopAiExercise, stopSimpleExercise]);

  const toggleMode = useCallback(async () => {
    if (aiMode === 'disabled') {
      await initializeAI();
    } else if (aiMode === 'enabled') {
      setAiMode('disabled');
      console.log('🔄 Switched to manual mode');
    }
  }, [aiMode, initializeAI]);

  // Return the appropriate values based on current mode
  const isActive = aiMode === 'enabled' ? (poses.length > 0 || aiRepCount > 0) : simpleIsActive;
  const feedback = aiMode === 'enabled' ? aiFeedback : simpleFeedback;
  const repCount = aiMode === 'enabled' ? aiRepCount : simpleRepCount;
  const exerciseInProgress = aiMode === 'enabled' ? currentExercise : simpleCurrentExercise;

  return {
    // State
    aiMode,
    isActive,
    currentExercise: exerciseInProgress,
    
    // Data
    poses,
    feedback,
    isCorrect,
    repCount,
    
    // Actions
    startExercise,
    stopExercise,
    toggleMode,
    initializeAI,
    
    // Manual controls (only work in manual mode)
    incrementRep: aiMode === 'disabled' ? incrementRep : () => {},
    decrementRep: aiMode === 'disabled' ? decrementRep : () => {},
  };
};
