import { useState, useCallback, useRef } from 'react';
import { Exercise, ExerciseFeedback } from '../types/exercise';

export const useSimpleExercise = () => {
  const [feedback, setFeedback] = useState<ExerciseFeedback | null>(null);
  const [repCount, setRepCount] = useState(0);
  const [isActive, setIsActive] = useState(false);
  const [currentExercise, setCurrentExercise] = useState<Exercise | null>(null);
  
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const startTimeRef = useRef<number>(0);

  const startExercise = useCallback((exercise: Exercise) => {
    console.log(`🏃‍♂️ Starting simple ${exercise} mode`);
    
    setCurrentExercise(exercise);
    setIsActive(true);
    setRepCount(0);
    startTimeRef.current = Date.now();
    
    setFeedback({
      message: `${exercise.charAt(0).toUpperCase() + exercise.slice(1)} started! Count your reps manually.`,
      type: 'success'
    });

    // Simple motivational messages every 30 seconds
    timerRef.current = setInterval(() => {
      const elapsed = Math.floor((Date.now() - startTimeRef.current) / 1000);
      const minutes = Math.floor(elapsed / 60);
      const seconds = elapsed % 60;
      
      const motivationalMessages = [
        "Keep going! You're doing great!",
        "Focus on your form!",
        "Push through! You've got this!",
        "Stay strong! Every rep counts!",
        "Breathe and maintain your rhythm!",
        "You're building strength with every rep!"
      ];
      
      const randomMessage = motivationalMessages[Math.floor(Math.random() * motivationalMessages.length)];
      
      setFeedback({
        message: `${randomMessage} (${minutes}:${seconds.toString().padStart(2, '0')})`,
        type: 'info'
      });
    }, 30000);

  }, []);

  const stopExercise = useCallback(() => {
    console.log('🛑 Stopping simple exercise mode');
    
    if (timerRef.current) {
      clearInterval(timerRef.current);
      timerRef.current = null;
    }
    
    const elapsed = Math.floor((Date.now() - startTimeRef.current) / 1000);
    const minutes = Math.floor(elapsed / 60);
    const seconds = elapsed % 60;
    
    setFeedback({
      message: `Great workout! Duration: ${minutes}:${seconds.toString().padStart(2, '0')}`,
      type: 'success'
    });
    
    // Clear feedback after 5 seconds
    setTimeout(() => {
      setFeedback(null);
      setIsActive(false);
      setCurrentExercise(null);
      setRepCount(0);
    }, 5000);
    
  }, []);

  const incrementRep = useCallback(() => {
    setRepCount(prev => {
      const newCount = prev + 1;
      setFeedback({
        message: `Rep ${newCount} completed! Keep it up!`,
        type: 'success'
      });
      return newCount;
    });
  }, []);

  const decrementRep = useCallback(() => {
    setRepCount(prev => {
      const newCount = Math.max(0, prev - 1);
      if (newCount === 0) {
        setFeedback({
          message: "Rep count reset. Keep going!",
          type: 'info'
        });
      } else {
        setFeedback({
          message: `Rep count: ${newCount}`,
          type: 'info'
        });
      }
      return newCount;
    });
  }, []);

  return {
    feedback,
    repCount,
    isActive,
    currentExercise,
    startExercise,
    stopExercise,
    incrementRep,
    decrementRep
  };
};
