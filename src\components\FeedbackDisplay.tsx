
import { Alert<PERSON>ircle, CheckCircle, AlertTriangle, Target } from 'lucide-react';
import { ExerciseFeedback, Exercise } from '../types/exercise';

interface FeedbackDisplayProps {
  feedback: ExerciseFeedback | null;
  isCorrect: boolean;
  currentExercise: Exercise | null;
}

export const FeedbackDisplay = ({ feedback, isCorrect, currentExercise }: FeedbackDisplayProps) => {
  if (!currentExercise) {
    return (
      <div className="bg-gray-800/50 backdrop-blur-sm rounded-xl p-6 text-center">
        <div className="text-gray-400 mb-4">
          <Target className="w-12 h-12 mx-auto mb-2" />
          <h3 className="text-lg font-semibold">Ready to Train</h3>
          <p className="text-sm">Select an exercise to begin your AI-guided workout</p>
        </div>
      </div>
    );
  }

  const getIcon = () => {
    if (!feedback) return null;
    
    switch (feedback.type) {
      case 'success':
        return <CheckCircle className="w-6 h-6 text-green-400" />;
      case 'warning':
        return <AlertTriangle className="w-6 h-6 text-yellow-400" />;
      case 'error':
        return <AlertCircle className="w-6 h-6 text-red-400" />;
      default:
        return null;
    }
  };

  const getBgColor = () => {
    if (!feedback) return 'bg-gray-800/50';
    
    switch (feedback.type) {
      case 'success':
        return 'bg-green-900/30 border-green-500/30';
      case 'warning':
        return 'bg-yellow-900/30 border-yellow-500/30';
      case 'error':
        return 'bg-red-900/30 border-red-500/30';
      default:
        return 'bg-gray-800/50';
    }
  };

  return (
    <div className={`${getBgColor()} backdrop-blur-sm rounded-xl p-6 border transition-all duration-300`}>
      <div className="flex items-start gap-3">
        {getIcon()}
        <div className="flex-1">
          <h3 className="font-semibold text-lg capitalize mb-2">
            {currentExercise} Form Analysis
          </h3>
          
          {feedback && (
            <>
              <p className="text-sm mb-3">{feedback.message}</p>
              
              {feedback.details && feedback.details.length > 0 && (
                <div className="space-y-1">
                  <p className="text-xs font-medium text-gray-400 uppercase tracking-wide">
                    Tips for improvement:
                  </p>
                  <ul className="text-xs space-y-1">
                    {feedback.details.map((detail, index) => (
                      <li key={index} className="flex items-start gap-2">
                        <span className="text-blue-400 mt-1">•</span>
                        <span>{detail}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </>
          )}
          
          <div className={`mt-4 px-3 py-2 rounded-lg text-center font-medium ${
            isCorrect 
              ? 'bg-green-500/20 text-green-400' 
              : 'bg-red-500/20 text-red-400'
          }`}>
            {isCorrect ? '✓ Perfect Form' : '⚠ Adjust Form'}
          </div>
        </div>
      </div>
    </div>
  );
};
