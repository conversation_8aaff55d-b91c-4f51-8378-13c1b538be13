
import { Pose, ExerciseFeedback, ExerciseState } from '../types/exercise';
import { calculateAngle } from './poseUtils';

export const evaluateSquats = (pose: Pose, state: ExerciseState) => {
  const landmarks = pose.landmarks;
  
  // Key landmarks for squat evaluation
  const leftHip = landmarks[23];
  const leftKnee = landmarks[25];
  const leftAnkle = landmarks[27];
  const leftShoulder = landmarks[11];
  
  const rightHip = landmarks[24];
  const rightKnee = landmarks[26];
  const rightAnkle = landmarks[28];
  const rightShoulder = landmarks[12];
  
  if (!leftHip || !leftKnee || !leftAnkle || !rightHip || !rightKnee || !rightAnkle) {
    return {
      feedback: { message: "Position yourself fully in the camera view", type: 'warning' as const },
      isCorrect: false,
      repCount: state.repCount,
      state
    };
  }
  
  // Calculate knee angles (hip-knee-ankle)
  const leftKneeAngle = calculateAngle(leftHip, leftKnee, leftAnkle);
  const rightKneeAngle = calculateAngle(rightHip, rightKnee, rightAnkle);
  const avgKneeAngle = (leftKneeAngle + rightKneeAngle) / 2;
  
  // Calculate back angle (shoulder-hip-knee)
  const leftBackAngle = calculateAngle(leftShoulder, leftHip, leftKnee);
  const rightBackAngle = calculateAngle(rightShoulder, rightHip, rightKnee);
  const avgBackAngle = (leftBackAngle + rightBackAngle) / 2;
  
  let feedback: ExerciseFeedback;
  let isCorrect = true;
  let newState = { ...state };
  
  // Determine squat phase
  const isInBottomPosition = avgKneeAngle < 100;
  const isInTopPosition = avgKneeAngle > 160;
  
  // Phase detection and rep counting
  if (state.phase === 'rest' && isInBottomPosition) {
    newState.phase = 'down';
  } else if (state.phase === 'down' && isInTopPosition) {
    newState.phase = 'up';
    newState.repCount += 1;
    newState.phase = 'rest';
  }
  
  // Form evaluation
  const issues: string[] = [];
  
  if (isInBottomPosition) {
    if (avgKneeAngle > 100) {
      issues.push("Go deeper - knees should bend more");
      isCorrect = false;
    }
    
    if (avgBackAngle < 150) {
      issues.push("Keep your back straight");
      isCorrect = false;
    }
    
    // Check knee alignment
    const kneeHipDistance = Math.abs(leftKnee.x - leftHip.x) + Math.abs(rightKnee.x - rightHip.x);
    if (kneeHipDistance > 0.1) {
      issues.push("Keep knees aligned with your hips");
      isCorrect = false;
    }
  }
  
  if (isCorrect && isInBottomPosition) {
    feedback = {
      message: "Perfect squat form! Hold this position.",
      type: 'success'
    };
  } else if (isCorrect) {
    feedback = {
      message: "Good form! Continue your movement.",
      type: 'success'
    };
  } else {
    feedback = {
      message: "Adjust your form",
      type: 'error',
      details: issues
    };
  }
  
  return {
    feedback,
    isCorrect,
    repCount: newState.repCount,
    state: newState
  };
};
