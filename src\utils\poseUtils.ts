
import { PoseLandmark } from '../types/exercise';

export const getPoseConnections = (): [number, number][] => {
  return [
    // Face
    [0, 1], [1, 2], [2, 3], [3, 7],
    [0, 4], [4, 5], [5, 6], [6, 8],
    
    // Arms
    [9, 10], [11, 12], [11, 13], [13, 15], [15, 17], [15, 19], [15, 21],
    [12, 14], [14, 16], [16, 18], [16, 20], [16, 22],
    
    // Body
    [11, 23], [12, 24], [23, 24],
    
    // Legs
    [23, 25], [25, 27], [27, 29], [29, 31],
    [24, 26], [26, 28], [28, 30], [30, 32]
  ];
};

export const getLandmarkPosition = (landmark: PoseLandmark): [number, number, number] => {
  return [landmark.x, -landmark.y, landmark.z];
};

export const calculateAngle = (
  a: PoseLandmark,
  b: PoseLandmark,
  c: PoseLandmark
): number => {
  const ab = Math.sqrt(Math.pow(b.x - a.x, 2) + Math.pow(b.y - a.y, 2));
  const bc = Math.sqrt(Math.pow(c.x - b.x, 2) + Math.pow(c.y - b.y, 2));
  const ac = Math.sqrt(Math.pow(c.x - a.x, 2) + Math.pow(c.y - a.y, 2));
  
  const angle = Math.acos((ab * ab + bc * bc - ac * ac) / (2 * ab * bc));
  return angle * (180 / Math.PI);
};

export const calculateDistance = (a: PoseLandmark, b: PoseLandmark): number => {
  return Math.sqrt(Math.pow(b.x - a.x, 2) + Math.pow(b.y - a.y, 2));
};
