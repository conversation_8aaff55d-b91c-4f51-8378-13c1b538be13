
import { Pose, ExerciseFeedback, ExerciseState } from '../types/exercise';
import { calculateAngle } from './poseUtils';

export const evaluatePushUps = (pose: Pose, state: ExerciseState) => {
  const landmarks = pose.landmarks;
  
  // Key landmarks for push-up evaluation
  const leftShoulder = landmarks[11];
  const leftElbow = landmarks[13];
  const leftWrist = landmarks[15];
  const leftHip = landmarks[23];
  const leftAnkle = landmarks[27];
  
  const rightShoulder = landmarks[12];
  const rightElbow = landmarks[14];
  const rightWrist = landmarks[16];
  const rightHip = landmarks[24];
  const rightAnkle = landmarks[28];
  
  if (!leftShoulder || !leftElbow || !leftWrist || !rightShoulder || !rightElbow || !rightWrist) {
    return {
      feedback: { message: "Position yourself fully in the camera view", type: 'warning' as const },
      isCorrect: false,
      repCount: state.repCount,
      state
    };
  }
  
  // Calculate elbow angles
  const leftElbowAngle = calculateAngle(leftShoulder, leftElbow, leftWrist);
  const rightElbowAngle = calculateAngle(rightShoulder, rightElbow, rightWrist);
  const avgElbowAngle = (leftElbowAngle + rightElbowAngle) / 2;
  
  // Calculate body alignment (shoulder-hip-ankle line)
  const leftBodyAngle = calculateAngle(leftShoulder, leftHip, leftAnkle);
  const rightBodyAngle = calculateAngle(rightShoulder, rightHip, rightAnkle);
  const avgBodyAngle = (leftBodyAngle + rightBodyAngle) / 2;
  
  let feedback: ExerciseFeedback;
  let isCorrect = true;
  let newState = { ...state };
  
  // Determine push-up phase
  const isInBottomPosition = avgElbowAngle < 90;
  const isInTopPosition = avgElbowAngle > 150;
  
  // Phase detection and rep counting
  if (state.phase === 'rest' && isInBottomPosition) {
    newState.phase = 'down';
  } else if (state.phase === 'down' && isInTopPosition) {
    newState.phase = 'up';
    newState.repCount += 1;
    newState.phase = 'rest';
  }
  
  // Form evaluation
  const issues: string[] = [];
  
  // Check elbow angle during descent
  if (isInBottomPosition && avgElbowAngle > 90) {
    issues.push("Lower yourself more - elbows should be at 90 degrees");
    isCorrect = false;
  }
  
  // Check body alignment
  if (Math.abs(avgBodyAngle - 180) > 20) {
    issues.push("Keep your body in a straight line");
    isCorrect = false;
  }
  
  // Check hand position relative to shoulders
  const leftHandShoulderDistance = Math.abs(leftWrist.x - leftShoulder.x);
  const rightHandShoulderDistance = Math.abs(rightWrist.x - rightShoulder.x);
  
  if (leftHandShoulderDistance > 0.15 || rightHandShoulderDistance > 0.15) {
    issues.push("Position hands directly under your shoulders");
    isCorrect = false;
  }
  
  if (isCorrect && isInBottomPosition) {
    feedback = {
      message: "Perfect push-up form! Now push up.",
      type: 'success'
    };
  } else if (isCorrect) {
    feedback = {
      message: "Excellent form! Keep it up.",
      type: 'success'
    };
  } else {
    feedback = {
      message: "Adjust your form",
      type: 'error',
      details: issues
    };
  }
  
  return {
    feedback,
    isCorrect,
    repCount: newState.repCount,
    state: newState
  };
};
