
import { Button } from '@/components/ui/button';
import { Play, Square, Zap, Target } from 'lucide-react';
import { Exercise } from '../types/exercise';

interface ExerciseControlsProps {
  onStartSquats: () => void;
  onStartPushUps: () => void;
  onStop: () => void;
  isActive: boolean;
  currentExercise: Exercise | null;
}

export const ExerciseControls = ({
  onStartSquats,
  onStartPushUps,
  onStop,
  isActive,
  currentExercise
}: ExerciseControlsProps) => {
  return (
    <div className="flex flex-wrap gap-4 justify-center">
      {!isActive ? (
        <>
          <Button
            onClick={onStartSquats}
            className="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white px-8 py-4 text-lg font-semibold rounded-xl shadow-lg transform transition-all duration-200 hover:scale-105"
          >
            <Target className="w-6 h-6 mr-2" />
            Start Squats
          </Button>
          <Button
            onClick={onStartPushUps}
            className="bg-gradient-to-r from-emerald-500 to-emerald-600 hover:from-emerald-600 hover:to-emerald-700 text-white px-8 py-4 text-lg font-semibold rounded-xl shadow-lg transform transition-all duration-200 hover:scale-105"
          >
            <Zap className="w-6 h-6 mr-2" />
            Start Push-Ups
          </Button>
        </>
      ) : (
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2 text-lg">
            <div className="w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
            <span className="capitalize font-semibold">
              {currentExercise} in progress
            </span>
          </div>
          <Button
            onClick={onStop}
            variant="destructive"
            className="px-6 py-3 text-lg font-semibold rounded-xl shadow-lg"
          >
            <Square className="w-5 h-5 mr-2" />
            Stop
          </Button>
        </div>
      )}
    </div>
  );
};
