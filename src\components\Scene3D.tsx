
import { Canvas } from '@react-three/fiber';
import { OrbitControls, Text, Html } from '@react-three/drei';
import { Pose, ExerciseFeedback } from '../types/exercise';
import { PoseVisualization } from './PoseVisualization';
import { FeedbackArrows } from './FeedbackArrows';

interface Scene3DProps {
  poses: Pose[];
  feedback: ExerciseFeedback | null;
  isCorrect: boolean;
}

export const Scene3D = ({ poses, feedback, isCorrect }: Scene3DProps) => {
  return (
    <div className="w-full h-full relative">
      <Canvas camera={{ position: [0, 0, 5], fov: 60 }}>
        <ambientLight intensity={0.5} />
        <pointLight position={[10, 10, 10]} />
        
        {/* 3D Pose Visualization */}
        {poses.length > 0 && (
          <PoseVisualization 
            pose={poses[0]} 
            isCorrect={isCorrect}
          />
        )}
        
        {/* Feedback Arrows and Tips */}
        {feedback && !isCorrect && (
          <FeedbackArrows feedback={feedback} />
        )}
        
        {/* Success Message */}
        {isCorrect && (
          <Text
            position={[0, 2, 0]}
            fontSize={0.5}
            color="#10b981"
            anchorX="center"
            anchorY="middle"
          >
            Perfect Form!
          </Text>
        )}
        
        <OrbitControls
          enablePan={false}
          enableZoom={false}
          autoRotate={!isCorrect}
          autoRotateSpeed={1}
        />
      </Canvas>
      
      {/* Overlay Instructions */}
      <div className="absolute top-4 left-4 text-xs text-gray-400">
        3D Form Analysis
      </div>
    </div>
  );
};
