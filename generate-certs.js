#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🔐 Generating self-signed certificates for HTTPS development...');

try {
  // Check if certificates already exist
  if (fs.existsSync('./localhost.pem') && fs.existsSync('./localhost-key.pem')) {
    console.log('✅ Certificates already exist!');
    process.exit(0);
  }

  // Check if OpenSSL is available
  try {
    execSync('openssl version', { stdio: 'ignore' });
  } catch (error) {
    console.log('❌ OpenSSL not found. Please install OpenSSL or use mkcert instead.');
    console.log('Alternative: Install mkcert from https://github.com/FiloSottile/mkcert');
    process.exit(1);
  }

  // Generate self-signed certificate
  const command = `openssl req -x509 -newkey rsa:4096 -keyout localhost-key.pem -out localhost.pem -days 365 -nodes -subj "/C=US/ST=State/L=City/O=Organization/CN=localhost"`;
  
  execSync(command, { stdio: 'inherit' });
  
  console.log('✅ Self-signed certificates generated successfully!');
  console.log('📁 Files created:');
  console.log('   - localhost.pem (certificate)');
  console.log('   - localhost-key.pem (private key)');
  console.log('');
  console.log('⚠️  Note: Your browser will show a security warning for self-signed certificates.');
  console.log('   Click "Advanced" and "Proceed to localhost" to continue.');
  console.log('');
  console.log('🚀 Now restart your dev server with: npm run dev');

} catch (error) {
  console.error('❌ Error generating certificates:', error.message);
  console.log('');
  console.log('💡 Alternative solutions:');
  console.log('1. Use mkcert: https://github.com/FiloSottile/mkcert');
  console.log('2. Use Chrome with --unsafely-treat-insecure-origin-as-secure flag');
  console.log('3. Test on localhost (which is considered secure)');
  process.exit(1);
}
