import { forwardRef, useState, useCallback, useRef } from 'react';
import { Camera, CameraOff, Play, Square, AlertCircle } from 'lucide-react';

interface SimpleCameraProps {
  className?: string;
  onCameraStateChange?: (isRunning: boolean) => void;
}

export const SimpleCamera = forwardRef<HTMLVideoElement, SimpleCameraProps>(
  ({ className = '', onCameraStateChange }, ref) => {
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [isRunning, setIsRunning] = useState(false);
    const streamRef = useRef<MediaStream | null>(null);

    const startCamera = useCallback(async () => {
      console.log('Starting simple camera...');
      setIsLoading(true);
      setError(null);

      try {
        // Stop any existing stream
        if (streamRef.current) {
          streamRef.current.getTracks().forEach(track => track.stop());
          streamRef.current = null;
        }

        // Get camera stream with simple settings
        const stream = await navigator.mediaDevices.getUserMedia({
          video: {
            width: { ideal: 640 },
            height: { ideal: 480 }
          }
        });

        console.log('Simple camera stream obtained');
        streamRef.current = stream;

        if (ref && typeof ref === 'object' && ref.current) {
          ref.current.srcObject = stream;
          
          ref.current.onloadedmetadata = () => {
            console.log('Simple camera video loaded');
            setIsLoading(false);
            setIsRunning(true);
            onCameraStateChange?.(true);
            
            if (ref && typeof ref === 'object' && ref.current) {
              ref.current.play().catch(console.error);
            }
          };

          ref.current.onerror = (err) => {
            console.error('Simple camera video error:', err);
            setError('Video playback error');
            setIsLoading(false);
          };
        }
      } catch (err) {
        console.error('Simple camera error:', err);
        setIsLoading(false);
        setIsRunning(false);
        onCameraStateChange?.(false);
        
        if (err instanceof Error) {
          setError(`Camera error: ${err.message}`);
        } else {
          setError('Failed to start camera');
        }
      }
    }, [ref, onCameraStateChange]);

    const stopCamera = useCallback(() => {
      console.log('Stopping simple camera...');
      
      if (streamRef.current) {
        streamRef.current.getTracks().forEach(track => track.stop());
        streamRef.current = null;
      }
      
      if (ref && typeof ref === 'object' && ref.current) {
        ref.current.srcObject = null;
      }
      
      setIsRunning(false);
      setError(null);
      onCameraStateChange?.(false);
    }, [ref, onCameraStateChange]);

    if (error) {
      return (
        <div className="w-full h-96 bg-gray-800 rounded-lg flex items-center justify-center">
          <div className="text-center p-6">
            <CameraOff className="w-16 h-16 text-red-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-white mb-2">Camera Error</h3>
            <p className="text-red-400 text-sm mb-4">{error}</p>
            <button
              onClick={startCamera}
              className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 flex items-center gap-2 mx-auto"
            >
              <Play className="w-4 h-4" />
              Try Again
            </button>
          </div>
        </div>
      );
    }

    if (isLoading) {
      return (
        <div className="w-full h-96 bg-gray-800 rounded-lg flex items-center justify-center">
          <div className="text-center p-6">
            <Camera className="w-16 h-16 text-blue-400 mx-auto mb-4 animate-pulse" />
            <h3 className="text-lg font-semibold text-white mb-2">Starting Camera</h3>
            <p className="text-gray-400">Please allow camera permissions...</p>
          </div>
        </div>
      );
    }

    if (!isRunning) {
      return (
        <div className="w-full h-96 bg-gray-800 rounded-lg flex items-center justify-center">
          <div className="text-center p-6">
            <Camera className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-white mb-2">Camera Ready</h3>
            <p className="text-gray-400 mb-4">Click start to begin camera feed</p>
            <button
              onClick={startCamera}
              className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 flex items-center gap-2 mx-auto"
            >
              <Play className="w-4 h-4" />
              Start Camera
            </button>
          </div>
        </div>
      );
    }

    return (
      <div className="relative">
        <video
          ref={ref}
          className={`w-full h-auto max-w-full bg-gray-900 rounded-lg ${className}`}
          autoPlay
          playsInline
          muted
          style={{ transform: 'scaleX(-1)' }}
        />
        
        <div className="absolute top-4 right-4 flex gap-2">
          <button
            onClick={startCamera}
            disabled={isLoading || isRunning}
            className="px-3 py-2 bg-green-600/80 text-white rounded hover:bg-green-700/80 disabled:opacity-50 flex items-center gap-1 text-sm backdrop-blur-sm"
          >
            <Play className="w-3 h-3" />
            Start
          </button>
          <button
            onClick={stopCamera}
            disabled={!isRunning}
            className="px-3 py-2 bg-red-600/80 text-white rounded hover:bg-red-700/80 disabled:opacity-50 flex items-center gap-1 text-sm backdrop-blur-sm"
          >
            <Square className="w-3 h-3" />
            Stop
          </button>
          <div className="flex items-center gap-1 px-2 py-1 bg-gray-800/80 rounded text-xs backdrop-blur-sm">
            <div className={`w-2 h-2 rounded-full ${isRunning ? 'bg-green-400 animate-pulse' : 'bg-gray-500'}`}></div>
            <span className="text-gray-300">{isRunning ? 'Live' : 'Off'}</span>
          </div>
        </div>
      </div>
    );
  }
);

SimpleCamera.displayName = 'SimpleCamera';
