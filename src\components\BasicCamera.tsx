import { forwardRef, useState, useRef, useEffect } from 'react';

interface BasicCameraProps {
  onCameraStateChange?: (isRunning: boolean) => void;
}

export const BasicCamera = forwardRef<HTMLVideoElement, BasicCameraProps>(
  ({ onCameraStateChange }, ref) => {
    const [isStarted, setIsStarted] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const streamRef = useRef<MediaStream | null>(null);

    const startCamera = async () => {
      try {
        console.log('Starting basic camera...');
        setError(null);

        // Check if getUserMedia is available
        if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
          throw new Error('Camera not supported in this browser');
        }

        console.log('Requesting camera access...');
        const stream = await navigator.mediaDevices.getUserMedia({
          video: {
            width: { ideal: 640, min: 320 },
            height: { ideal: 480, min: 240 },
            facingMode: 'user'
          }
        });

        console.log('Got stream:', {
          id: stream.id,
          active: stream.active,
          tracks: stream.getVideoTracks().length,
          settings: stream.getVideoTracks()[0]?.getSettings()
        });

        streamRef.current = stream;

        if (ref && typeof ref === 'object' && ref.current) {
          const video = ref.current;
          console.log('Setting stream to video element');

          // Set video properties first
          video.autoplay = true;
          video.playsInline = true;
          video.muted = true;

          video.srcObject = stream;

          const handleLoadedMetadata = async () => {
            console.log('Metadata loaded:', {
              videoWidth: video.videoWidth,
              videoHeight: video.videoHeight,
              readyState: video.readyState
            });

            try {
              await video.play();
              console.log('Video playing successfully');
              setIsStarted(true);
              onCameraStateChange?.(true);
            } catch (playError) {
              console.error('Play error:', playError);
              setError('Failed to play video: ' + playError);
            }
          };

          const handleError = (err: any) => {
            console.error('Video error:', err);
            setError('Video error: ' + err);
          };

          video.addEventListener('loadedmetadata', handleLoadedMetadata);
          video.addEventListener('error', handleError);

          // Try to play immediately as well
          video.play().catch(err => {
            console.log('Immediate play failed, waiting for metadata:', err);
          });
        }
      } catch (err) {
        console.error('Camera error:', err);
        let errorMsg = 'Camera failed';
        if (err instanceof Error) {
          if (err.name === 'NotAllowedError') {
            errorMsg = 'Camera permission denied. Please allow camera access.';
          } else if (err.name === 'NotFoundError') {
            errorMsg = 'No camera found. Please connect a camera.';
          } else if (err.name === 'NotReadableError') {
            errorMsg = 'Camera is being used by another application.';
          } else {
            errorMsg = err.message;
          }
        }
        setError(errorMsg);
      }
    };

    const stopCamera = () => {
      if (streamRef.current) {
        streamRef.current.getTracks().forEach(track => track.stop());
        streamRef.current = null;
      }
      if (ref && typeof ref === 'object' && ref.current) {
        ref.current.srcObject = null;
      }
      setIsStarted(false);
      onCameraStateChange?.(false);
    };

    useEffect(() => {
      return () => {
        stopCamera();
      };
    }, []);

    if (error) {
      return (
        <div className="w-full h-96 bg-red-900 rounded-lg flex items-center justify-center">
          <div className="text-center p-4">
            <p className="text-red-300 mb-4">{error}</p>
            <button
              onClick={startCamera}
              className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
            >
              Try Again
            </button>
          </div>
        </div>
      );
    }

    return (
      <div className="w-full">
        <div className="mb-4 flex gap-2">
          <button
            onClick={startCamera}
            disabled={isStarted}
            className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 disabled:opacity-50"
          >
            Start Camera
          </button>
          <button
            onClick={stopCamera}
            disabled={!isStarted}
            className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 disabled:opacity-50"
          >
            Stop Camera
          </button>
          <span className="px-3 py-2 bg-gray-700 text-white rounded">
            {isStarted ? '🟢 Running' : '🔴 Stopped'}
          </span>
        </div>
        
        <video
          ref={ref}
          className="w-full max-w-2xl bg-black border-2 border-gray-600 rounded"
          autoPlay
          playsInline
          muted
          style={{
            transform: 'scaleX(-1)',
            minHeight: '400px'
          }}
        />
      </div>
    );
  }
);

BasicCamera.displayName = 'BasicCamera';
