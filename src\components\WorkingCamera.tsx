import { forwardRef, useState, useRef, useEffect } from 'react';
import { Play, Square } from 'lucide-react';

interface WorkingCameraProps {
  onCameraStateChange?: (isRunning: boolean) => void;
}

export const WorkingCamera = forwardRef<HTMLVideoElement, WorkingCameraProps>(
  ({ onCameraStateChange }, ref) => {
    const [isActive, setIsActive] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const streamRef = useRef<MediaStream | null>(null);

    const startCamera = async () => {
      try {
        setError(null);
        console.log('🎥 Starting camera...');

        // Get the camera stream
        const stream = await navigator.mediaDevices.getUserMedia({
          video: {
            width: { ideal: 640 },
            height: { ideal: 480 },
            facingMode: 'user'
          }
        });

        console.log('✅ Camera stream obtained');
        streamRef.current = stream;

        if (ref && typeof ref === 'object' && ref.current) {
          const video = ref.current;
          
          // Set video element properties
          video.srcObject = stream;
          video.autoplay = true;
          video.playsInline = true;
          video.muted = true;

          // Wait for the video to be ready and play it
          video.onloadedmetadata = async () => {
            console.log('📹 Video metadata loaded:', {
              videoWidth: video.videoWidth,
              videoHeight: video.videoHeight,
              readyState: video.readyState
            });

            try {
              await video.play();
              console.log('✅ Video is playing successfully');
              setIsActive(true);
              onCameraStateChange?.(true);
            } catch (playErr) {
              console.error('❌ Play error:', playErr);
              setError('Could not play video');
            }
          };

          video.onerror = (err) => {
            console.error('❌ Video error:', err);
            setError('Video error occurred');
          };
        }
      } catch (err) {
        console.error('❌ Camera error:', err);
        if (err instanceof Error) {
          if (err.name === 'NotAllowedError') {
            setError('Camera permission denied. Please allow camera access.');
          } else if (err.name === 'NotFoundError') {
            setError('No camera found. Please connect a camera.');
          } else {
            setError(`Camera error: ${err.message}`);
          }
        } else {
          setError('Unknown camera error');
        }
      }
    };

    const stopCamera = () => {
      console.log('🛑 Stopping camera...');
      
      if (streamRef.current) {
        streamRef.current.getTracks().forEach(track => {
          track.stop();
          console.log('🛑 Stopped track:', track.kind);
        });
        streamRef.current = null;
      }

      if (ref && typeof ref === 'object' && ref.current) {
        ref.current.srcObject = null;
      }

      setIsActive(false);
      onCameraStateChange?.(false);
    };

    // Cleanup on unmount
    useEffect(() => {
      return () => {
        stopCamera();
      };
    }, []);

    if (error) {
      return (
        <div className="w-full h-96 bg-red-900/20 border border-red-500 rounded-lg flex items-center justify-center">
          <div className="text-center p-6">
            <div className="text-red-400 text-4xl mb-4">⚠️</div>
            <h3 className="text-lg font-semibold text-white mb-2">Camera Error</h3>
            <p className="text-red-300 text-sm mb-4">{error}</p>
            <button
              onClick={startCamera}
              className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
            >
              Try Again
            </button>
          </div>
        </div>
      );
    }

    return (
      <div className="relative w-full">
        {/* Control buttons */}
        <div className="mb-4 flex gap-3">
          <button
            onClick={startCamera}
            disabled={isActive}
            className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2 transition-colors"
          >
            <Play className="w-4 h-4" />
            Start Camera
          </button>
          <button
            onClick={stopCamera}
            disabled={!isActive}
            className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2 transition-colors"
          >
            <Square className="w-4 h-4" />
            Stop Camera
          </button>
          <div className="flex items-center gap-2 px-3 py-2 bg-gray-700 rounded">
            <div className={`w-3 h-3 rounded-full ${isActive ? 'bg-green-400 animate-pulse' : 'bg-gray-500'}`}></div>
            <span className="text-white text-sm">{isActive ? 'Live' : 'Stopped'}</span>
          </div>
        </div>

        {/* Video element */}
        <div className="relative bg-black rounded-lg overflow-hidden">
          <video
            ref={ref}
            className="w-full h-auto"
            style={{
              transform: 'scaleX(-1)', // Mirror the video
              minHeight: '300px',
              maxHeight: '500px',
              objectFit: 'cover'
            }}
            autoPlay
            playsInline
            muted
          />
          
          {!isActive && (
            <div className="absolute inset-0 flex items-center justify-center bg-gray-800">
              <div className="text-center">
                <div className="text-6xl mb-4">📹</div>
                <p className="text-gray-300">Click "Start Camera" to begin</p>
              </div>
            </div>
          )}
        </div>
      </div>
    );
  }
);

WorkingCamera.displayName = 'WorkingCamera';
