import { CameraTest } from '../components/CameraTest';

const CameraTestPage = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-gray-900 text-white">
      <div className="container mx-auto px-4 py-8">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-400 to-emerald-400 bg-clip-text text-transparent mb-4">
            Camera Diagnostics
          </h1>
          <p className="text-lg text-gray-300 max-w-2xl mx-auto">
            Test your camera functionality and troubleshoot issues
          </p>
        </div>
        
        <CameraTest />
        
        <div className="mt-8 max-w-2xl mx-auto">
          <div className="bg-gray-800 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-white mb-4">Troubleshooting Tips</h3>
            <div className="text-sm text-gray-300 space-y-2">
              <p>• <strong>Permission Denied:</strong> Click the camera icon in your browser's address bar and allow camera access</p>
              <p>• <strong>No Camera Found:</strong> Make sure your camera is connected and not being used by another application</p>
              <p>• <strong>Not Secure Context:</strong> Use HTTPS or localhost for full camera support</p>
              <p>• <strong>Camera In Use:</strong> Close other applications that might be using your camera</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CameraTestPage;
